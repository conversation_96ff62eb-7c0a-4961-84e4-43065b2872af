package ch.mks.wta4.dealer.ui;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableMBeanExport;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.servlet.config.annotation.CorsRegistration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import ch.mks.wta4.common.metrics.MetricsServlet;
import ch.mks.wta4.common.metrics.MicrometerService;
import ch.mks.wta4.dealer.ui.simulators.UISimulatedUMClient;
import ch.mks.wta4.um.remote.IUMClient;
import ch.mks.wta4.umgrpc.client.UMGRPCClient;
import ch.qos.logback.classic.helpers.MDCInsertingServletFilter;
import jakarta.servlet.Filter;

@SpringBootApplication
@EnableScheduling
@EnableCaching
@EnableMBeanExport
public class DealerUIBackendApplication implements WebMvcConfigurer {

    private final static Logger LOG = LoggerFactory.getLogger(DealerUIBackendApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(DealerUIBackendApplication.class, args);
    }

    @Value("${um.grpc.host}")
    private String umGrpcHost;

    @Value("${um.grpc.port}")
    private int umGrpcPort;

    @Value("${um.grpc.secret}")
    private String umGrpcSecret;

    @Value("${simulator.active}")
    private boolean simulateUM;

    @Value("${simulator.disconnect.active}")
    private boolean simulateDisconnectUM;

    @Value("${simulator.disconnect.period}")
    private long simulatorDisconnectPeriod;

    @Value("${simulator.disconnect.duration}")
    private long simulatorDisconnectDuration;

    @Value("${simulator.broadcast.active}")
    private boolean simulateBroadcast;
    
    @Value("${mksAppId}")
    private String mksAppId;

    @Bean
    public SessionManager sessionManager() {
        return new SessionManager();
    }

    @Bean
    public RequestInterceptor requestInterceptor() {
        return new RequestInterceptor();
    }

    @Override public void addCorsMappings(final CorsRegistry registry) {
        CorsRegistration corsRegistration = registry.addMapping("/**");
        corsRegistration
            .allowedMethods("*")
            .allowCredentials(true)
            .allowedOriginPatterns("*");
    }

    @Override public void addInterceptors(final InterceptorRegistry registry) {
        registry.addInterceptor(this.requestInterceptor());
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("sounds/**").addResourceLocations("classpath:/public/sounds/").setCachePeriod(60*24*7); // 1 week
        registry.addResourceHandler("favicon.ico").addResourceLocations("classpath:/public/").setCachePeriod(60*24*7); // 1 week
    }

    @Bean
    public IUMClient umClient() {
        if ( simulateUM ) {
            return new UISimulatedUMClient(simulateDisconnectUM, simulatorDisconnectPeriod / 1000, simulatorDisconnectDuration / 1000, simulateBroadcast);
        } else {
            return new UMGRPCClient(mksAppId, umGrpcSecret, umGrpcHost, umGrpcPort);
        }
    }

    @Bean
    public MicrometerService micrometerService() {
        return new MicrometerService();
    }

    @Bean
    public ServletRegistrationBean<MetricsServlet> metricsEndPointServletRegistration() {
        ServletRegistrationBean<MetricsServlet> bean = new ServletRegistrationBean<>(new MetricsServlet(), "/metrics/*");
        bean.setLoadOnStartup(1);
        return bean;
    }

    @EventListener(classes = { ContextRefreshedEvent.class })
    public void start() {
        try{
            LOG.info("start -> ");
            micrometerService().startSync();
            sessionManager().setClientService(umClient());
            if ( umClient() instanceof UMGRPCClient ) {
                ((UMGRPCClient)umClient()).startSync();
            }

            LOG.info("start <-");
        } catch(Exception e){
            LOG.error("start -", e);
            throw new RuntimeException(e);
        }
    }

    @EventListener(classes = { ContextClosedEvent.class })
    public void stop() {
        try{
            LOG.info("stop -> ");
            if ( umClient() instanceof UMGRPCClient ) {
                ((UMGRPCClient)umClient()).stopSync();
            }
            micrometerService().stopSync();
            LOG.info("stop <-");
        } catch(Exception e){
            LOG.error("stop -", e);
        }
    }
    
    @Bean
    public Filter mdcHeaderFilter() {
        return new MDCInsertingServletFilter();
    }
}
