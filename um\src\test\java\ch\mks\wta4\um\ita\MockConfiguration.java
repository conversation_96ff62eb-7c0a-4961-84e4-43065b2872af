package ch.mks.wta4.um.ita;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.mks.wta4.common.constants.ReferenceKey;
import ch.mks.wta4.common.constants.ReferenceType;
import ch.mks.wta4.common.time.TimeUtils;
import ch.mks.wta4.configuration.ICachedConfiguration;
import ch.mks.wta4.configuration.model.AuctionCommissionOverride;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration.ValidityMode;
import ch.mks.wta4.configuration.model.Band;
import ch.mks.wta4.configuration.model.Band.SpreadType;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.BusinessUnit.BusinessUnitStatus;
import ch.mks.wta4.configuration.model.BusinessUnitUserProfile;
import ch.mks.wta4.configuration.model.Category;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.Currency;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.CurrencyPair.BasePriceComputationMode;
import ch.mks.wta4.configuration.model.DefaultFixConfiguration;
import ch.mks.wta4.configuration.model.Device;
import ch.mks.wta4.configuration.model.FixSessionConfiguration;
import ch.mks.wta4.configuration.model.HedgeProfile;
import ch.mks.wta4.configuration.model.IHolidayCalendar;
import ch.mks.wta4.configuration.model.Limit;
import ch.mks.wta4.configuration.model.MessageType;
import ch.mks.wta4.configuration.model.OfflinePriceConfiguration;
import ch.mks.wta4.configuration.model.PipConfiguration;
import ch.mks.wta4.configuration.model.PriceVariationThresholdOverride;
import ch.mks.wta4.configuration.model.Product;
import ch.mks.wta4.configuration.model.RFQConfiguration;
import ch.mks.wta4.configuration.model.RFQConfiguration.MarginType;
import ch.mks.wta4.configuration.model.RFQConfiguration.RFQMode;
import ch.mks.wta4.configuration.model.Reference;
import ch.mks.wta4.configuration.model.Spread;
import ch.mks.wta4.configuration.model.StaticPrice;
import ch.mks.wta4.configuration.model.StaticPrice.StaticPriceType;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.configuration.model.UserAgreement;
import ch.mks.wta4.configuration.model.UserPreferenceType;
import ch.mks.wta4.configuration.model.wta.LiquidityProvider;
import ch.mks.wta4.dealercontrol.model.HedgingConfiguration;
import ch.mks.wta4.event.PrimaryStatus;
import ch.mks.wta4.ita.model.CacheRefreshInstruction;
import ch.mks.wta4.ita.model.CurrencyType;
import ch.mks.wta4.ita.model.ForwardCurve;
import ch.mks.wta4.ita.model.HedgingMode;
import ch.mks.wta4.ita.model.HedgingOperation;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.SpecificTime;
import ch.mks.wta4.ita.model.TimeWithZoneId;
import ch.mks.wta4.ita.model.TradingStatus;
import ch.mks.wta4.ita.model.marketStatus.MarketStatusSchedule;
import ch.mks.wta4.ita.model.marketStatus.MarketStatusScheduleFactory;
import ch.mks.wta4.messaging.amq.ActiveMQLocalBroker;
import ch.mks.wta4.priceprovider.IMarketDataSnapshotProvider;
import ch.mks.wta4.um.calendar.MKSCustomHolidayCalendar;

public class MockConfiguration implements ICachedConfiguration {

	private static final Logger LOG = LoggerFactory.getLogger(MockConfiguration.class);
	
    public Map<String, BusinessUnit> businessUnits = new HashMap<>();
    public Map<String, User> users = new HashMap<>();
    public List<CurrencyPair> currencyPairs = new ArrayList<CurrencyPair>();
    public Map<String, Set<Product>> products = new HashMap<>();
    public Map<OrderType, HedgingMode> hedgingModeByOrderType = new HashMap<>();
    public Map<String, Category> cateboryById = new HashMap<>();
    public BusinessUnit rootBU;
    public User systemUser;
    
    public static String EMPTY_CATEGORY_ID = "EMPTY_CATEGORY_ID";
    public static String NULL_CATEGORY_ID = "NULL_CATEGORY_ID";
    
    public Map<Channel,BUDistributionConfiguration> defaultMDSValidities = new HashMap<>();
    private PipConfiguration overridenBUPipConfiguration;
    private Long orderMinimumMinutesToExpire = 0l;
    
    private List<OrderType> allowedOrderTypes = Arrays.asList(OrderType.values());
    private boolean isEODActivated = true;
    
    private boolean marketClosed = false;
    private OfflinePriceConfiguration offlinePriceConfig;
    private Double crossedPriceThresholdPct = 0.005d;
    private IHolidayCalendar holidayCalendar = new MKSCustomHolidayCalendar(new HashMap<>());
    
    public MockConfiguration() {
        initCurrencyPairs();
        initBusinessUnits();
        initSystemUser();
        initMDSValidity();
    }

    private void initMDSValidity() {
        defaultMDSValidities.put(Channel.WEB, new BUDistributionConfiguration(null, Channel.WEB, 3l, ValidityMode.QUOTEID, 3000l, 10l, false));
        defaultMDSValidities.put(Channel.API, new BUDistributionConfiguration(null, Channel.API, 10l, ValidityMode.QUOTEID, 100l, 10l, false));
        defaultMDSValidities.put(Channel.INTERNAL, new BUDistributionConfiguration(null, Channel.INTERNAL, 2l, ValidityMode.QUOTEID, 3000l, 6l, false));
        defaultMDSValidities.put(Channel.MOBILE, new BUDistributionConfiguration(null, Channel.MOBILE, 1l, ValidityMode.QUOTEID, 5000l, 5l, false));
    }

	private void initSystemUser() {
        systemUser = new User();
        systemUser.setUserId("WTA4-SystemUser");     
        systemUser.setMainBU(rootBU);
        systemUser = User.asDealer(systemUser);
        
        systemUser.setActive(true);
        Limit limit = new Limit();
        limit.setTransactionLimit(-1d);
        systemUser.setLimit(limit);
        
        BusinessUnitUserProfile buup = new BusinessUnitUserProfile();
        buup.setBusinessUnit(rootBU);
        buup.setCurrencyPairs(currencyPairs);
        buup.setProducts(products);
        systemUser.getBusinessUnitUserProfiles().put(rootBU.getBusinessUnitId(), buup);
        
        users.put(systemUser.getUserId(), systemUser);
    }

    private void initCurrencyPairs() {

        CurrencyPair cp = new CurrencyPair();
        cp.setCurrencyPairId("XAU/USD");
        cp.setPipConfiguration(new PipConfiguration(0.01, 1d));
        cp.setHedgingMinimumQuantity(0d);
        cp.setHedgingQuantityPrecision(0.00000000001d);
        cp.setTradingStatus(TradingStatus.TRADABLE);
        cp.setStaleThresholdInMillis(1000000000l);
        cp.setMinimumSpread(0d);
        cp.setBasePriceComputationMode(BasePriceComputationMode.MINIMUM_SPREAD);
        cp.setMinimumTradeQuantityInBaseUnits(0.001d);
        cp.setMaximumTradeQuantityInBaseUnits(20000d);
        
        Currency leftCurrency = new Currency();
        leftCurrency.setCurrencyId("XAU");
        leftCurrency.setCurrencyType(CurrencyType.METAL);
        
        Currency rightCurrency = new Currency();
        rightCurrency.setCurrencyId("USD");

        cp.setLeftCurrency(leftCurrency);
        cp.setRightCurrency(rightCurrency);
        currencyPairs.add(cp);

        Product product = new Product();
        product.setProductId("Gold Oz 995");
        product.setEquivalentBaseUnits(1d);
        product.setBaseQuantityPrecision(0.001d);
        product.setProductQuantityPrecision(0.001d);
        
        Set<Product> productSet = new HashSet<Product>();
        productSet.add(product);
        products.put(cp.getCurrencyPairId(), productSet);
    }

    private void initBusinessUnits() {
        for (int i = 0; i < 500; i++) {
            BusinessUnit bu = new BusinessUnit();
            bu.setBusinessUnitId("BU" + i);
            bu.setCurrencyPairs(currencyPairs);
            bu.setProducts(products);
            
            Limit bulimit = new Limit();
            bulimit.setTransactionLimit(-1d);
            bulimit.setDailyNetTransactionLimit(-1d);
            bu.setLimit(bulimit);
            
            if (i == 0) {
                rootBU = bu;
                bu.setParentBU(null);
            } else {
                bu.setParentBU(rootBU);
            }

            Category category = new Category();
            category.setCategoryId("CAT" + i);
            cateboryById.put(category.getCategoryId(), category);

            for (CurrencyPair cp : currencyPairs) {
                category.getSpreadByCurrencyPair().put(cp.getCurrencyPairId(), i==0?getSpread(cp.getCurrencyPairId(), 0):getSpread(cp.getCurrencyPairId(), 0.1));
            }
            
            bu.setCategoryId(category.getCategoryId());
            bu.setBusinessUnitStatus(BusinessUnitStatus.ACTIVE);
            bu.setForwardEnabled(true);

            businessUnits.put(bu.getBusinessUnitId(), bu);

            for (int j = 0; j < 10; j++) {
                User user = new User();
                user.setUserId("BU:" + bu.getBusinessUnitId() + "-Usr:" + j);
                BusinessUnitUserProfile buup = new BusinessUnitUserProfile();
                buup.setBusinessUnit(bu);
                buup.setCurrencyPairs(currencyPairs);
                buup.setProducts(products);
                user.getBusinessUnitUserProfiles().put(bu.getBusinessUnitId(), buup);
                
                user = User.asCustomer(user);
                user = User.asDealer(user);
                
                user.setActive(true);
                user.setMainBU(bu);
                
                Limit limit = new Limit();
                limit.setTransactionLimit(-1d);
                user.setLimit(limit);
                
                users.put(user.getUserId(), user);
            }
        }
    }

    private Spread getSpread(String currencyPairId, double offset) {
        Spread spread = new Spread();
        List<Double> bands = getBands(currencyPairId);
        for (Double bandQty : bands) {
            Band band = new Band(bandQty, offset + 0.01, offset + 0.02, SpreadType.ABSOLUTE);
            spread.getBands().add(band);
        }
        return spread;
    }

    @Override
    public User getUser(String userId) {
        return users.get(userId);
    }

    @Override
    public BusinessUnit getBusinessUnit(String buId) {
        return businessUnits.get(buId);
    }

    public void evictBusinessUnit(String buId) {}

    public void evictUser(String userId) {}

    public User getFirstUser() {
        return users.values().stream().findFirst().get();
    }
    
    public User getUserNotInFirstUserBU() {
        User firstUser = getFirstUser();
        String firstUserBuId = firstUser.getMainBU().getBusinessUnitId();
        return users.values().stream().filter(u -> ! u.getMainBU().getBusinessUnitId().equals(firstUserBuId) ).findFirst().get();
    }

    @Override
    public List<Double> getBands(String currencyPairId) {
        return Arrays.asList(new Double[] { 100d, 200d, 500d, 1000d, 2000d, 10000d });
    }
    
    @Override
    public HedgeProfile getHedgeProfile(String currencyPairId, OrderType orderType) {
        HedgingMode hedgingMode = hedgingModeByOrderType.get(orderType);
        
        HedgeProfile hedgeProfile = new HedgeProfile();
        hedgeProfile.setCurrencyPairId(currencyPairId);
        hedgeProfile.setOrderType(orderType);
        
        if ( hedgingMode != null ) {
            hedgeProfile.setHedgingMode(hedgingMode);
        } else {
            hedgeProfile.setHedgingMode(HedgingMode.MANUAL);
        }
        hedgeProfile.setHedgingOperation(HedgingOperation.ALL);
        return hedgeProfile;
    }

    public void setHedgingMode(OrderType orderType, HedgingMode hedgingMode) {
        hedgingModeByOrderType.put(orderType, hedgingMode);
    }

    @Override
    public BusinessUnit getRootBusinessUnit() {
        return rootBU;
    }
    
    public void setEODActivated(boolean isEODActivated) {
        this.isEODActivated = isEODActivated;
    }

    @Override
    public User getSystemUser() {
        return systemUser;
    }

    @Override
    public long getOrderMonitorGTDPollTimeMillis() {
        return 10;
    }

    public CurrencyPair getFirstCP() {
        return currencyPairs.stream().findFirst().get();
    }

	@Override
	public Spread getSpread(String categoryId, String currencyPairId) {
	    
	    
	    if ( categoryId.equals(EMPTY_CATEGORY_ID) ) {
	        return new Spread();
	    }
	    
	    if ( categoryId.equals(NULL_CATEGORY_ID) ) {
            return null;
        } 
	    
		Spread spread = cateboryById.get(categoryId).getSpread(currencyPairId);
		if ( spread == null ) {
		    spread = getSpread(currencyPairId, 0d);
		    cateboryById.get(categoryId).getSpreadByCurrencyPair().put(currencyPairId, spread);
		}
		return spread;
	}

	@Override
	public CurrencyPair getCurrencyPair(String currencyPairId) {
		return currencyPairs.stream().filter(cp -> cp.getCurrencyPairId().equals(currencyPairId)).findFirst().orElseGet(() -> {
			LOG.warn("No currency pair found: {}", currencyPairId);
			return null;
		});
	}

	public void evictCurrencyPair(String currencyPairId) {
		
	}

	@Override
	public List<LiquidityProvider> getLiquidityProviders() {
		return null;
	}

	@Override
	public int getLogonWaitTimeInSeconds() {
		return 0;
	}
    @Override
    public Product getHedingProduct(String currencyPairId) {
        return products.get(currencyPairId).stream().findFirst().get();
    }
    
    @Override
    public BUDistributionConfiguration getBUDistributionConfiguration(String buId, Channel channel) {
        return defaultMDSValidities.get(channel);
    }

    @Override
    public BusinessUnit getLPITABusinessUnit() {
        return getRootBusinessUnit();
    }

    @Override
    public TimeWithZoneId getEODHedgingClosingTime(String currencyPairId) {
        return TimeWithZoneId.get("17:00:00", ZoneId.of("America/New_York"));
    }

    public User getOneUserFromBU(String buId) {
        return users.values().parallelStream().filter(u -> buId.equals(u.getMainBU().getBusinessUnitId())).findAny().get();
    }
    
    public List<User> getAllUsersFromBU(String buId){
        return users.values().parallelStream().filter(u -> buId.equals(u.getMainBU().getBusinessUnitId())).collect(Collectors.toList());
    }

    @Override
    public PipConfiguration getOverriddenPipConfiguration(String buId, String currencyPairId) {
        return overridenBUPipConfiguration;
    }

    public void setOverridenPipConfiguration(PipConfiguration overridenBUPipConfiguration) {
        //same for any bu,cp
        this.overridenBUPipConfiguration = overridenBUPipConfiguration;
    }

    @Override public Product getProduct(String productId) {
        for(Set<Product> productSet:products.values()) {
            for(Product product:productSet) {
                if ( productId.equals(product.getProductId())) {
                    return product;
                }
            }
        }
        return null;
    }
    
    @Override public void evictBand(String currencyPair) {}
    @Override public void evictLiquidityProviders() {}
    @Override public void evictProduct(String productId) {}
    @Override public List<CurrencyPair> getAllCurrencyPairs() {
        return currencyPairs;
    }

    @Override
    public Double getOrderPriceCheckMargin(String currencyPairId, OrderType type) {
        return 5d;
    }

    @Override
    public int getHedgingCloserMaxRetries() {
        return 0;
    }

    @Override
    public int getHedgingCloserRetryPeriodInSeconds() {
        return 0;
    }

    @Override
    public long getOrderMinimumTimeSeparationInMillis() {
        return 0;
    }

    
    public void setMarketClosed(boolean marketClosed) {
        this.marketClosed = marketClosed;
    }
    
    @Override
    public MarketStatusSchedule getMarketStatusSchedule() {
        
        if (marketClosed) {
            String configuration = "SUNDAY 18:00:00,SUNDAY 18:00:01";
            ZoneId zoneId = ZoneId.of("America/New_York");
            return MarketStatusScheduleFactory.create(configuration, zoneId );
        } else {
            //always open
            String configuration = "";
            ZoneId zoneId = ZoneId.of("America/New_York");
            return MarketStatusScheduleFactory.create(configuration, zoneId );
        }
    }

    public User getDealer() {
        return getOneUserFromBU( rootBU.getBusinessUnitId() );
    }

    @Override
    public List<BusinessUnit> getAllBusinessUnits() {
        throw new RuntimeException("Not implemented");
    }
    
    @Override
    public List<BusinessUnit> getActiveBusinessUnits() {
        throw new RuntimeException("Not implemented");
    }

    @Override
    public List<User> getUsersByBusinessUnit(String buId) {
        throw new RuntimeException("Not implemented");
    }

    public Double getAuctionCommission(String buId, String currencyPairId, Operation mksOperation) {
        return 0d;
    }

    @Override
    public Long getAuctionMinimumMinutesToSessionTime(String currencyPairId) {
        return orderMinimumMinutesToExpire ;
    }

    public void setOrderMinimumMinutesToExpire(Long orderMinimumMinutesToExpire) {
        this.orderMinimumMinutesToExpire = orderMinimumMinutesToExpire;
    }

    @Override
    public List<LocalDate> getUKAuctionHolidays(String currencyId, SpecificTime specificTime) {
        return new ArrayList<>();
    }

    @Override
    public TimeWithZoneId getEODTime() {
        return new TimeWithZoneId(LocalTime.of(0,0,0), TimeUtils.GVA_ZONE_ID);
    }


    @Override
    public ZoneId getApplicationZoneId() {
        return ZoneId.systemDefault();
    }

    @Override
    public String getActiveOrderListCronExpression() {
        return "*/15 * * * mon-fri";
    }

    @Override
    public String getEODRecapCronExpression() {
        return "1 0 * * *";
    }

    @Override
    public long getOrderStaleCheckPeriodInSeconds() {
        return 60;
    }

    @Override
    public long getMaxStalledTimeInSeconds() {
        return 60;
    }

    @Override
    public String getSequentialIDGeneratorStoreFilePath() {
        return "var/seq/sequence.properties";
    }

    @Override
    public String getApplicationId() {
        return "wta4";
    }

	@Override
	public Reference getReferenceByTypeAndKey(ReferenceType type, ReferenceKey key) {
		return null;
	}

	@Override
	public List<BusinessUnit> getBusinessUnitsByTradingCategory(String categoryId) {
		return null;
	}

	@Override
	public String getProjectBuildType() {
		return null;
	}

	@Override
	public List<HedgeProfile> getHedgingProfileByOrderType(OrderType orderType) {
		return null;
	}

	@Override
	public void evictSpread(String categoryId, String currencyPairId) {
	}

	@Override
	public List<BusinessUnit> getBusinessUnitByUser(Integer userId) {
		return null;
	}

	@Override
	public void evictAllCurrencyPairs() {
	}

    @Override
    public List<OrderType> getAllowedOrderTypes(String buId, Channel channel) {
        return allowedOrderTypes;
    }
    
    public void setAllowedOrderTypes(List<OrderType> allowedOrderTypes) {
        this.allowedOrderTypes = allowedOrderTypes;
    }

    @Override
    public Reference getReferenceByTypeAndValue(ReferenceType type, String value) {
        throw new RuntimeException("Not implemented");
    }

    @Override
    public List<Reference> getReferenceListByType(ReferenceType type) {
        throw new RuntimeException("Not implemented");
    }

    @Override
    public void evictBusinessUnitByUser(Integer userId) {
        throw new RuntimeException("Not implemented");
    }

    

	@Override
	public Set<Currency> getAllCurrency() {
		return null;
	}

	@Override
	public Currency getCurrency(String currencyId) {
		return null;
	}

	@Override
	public UserAgreement getActiveUserAgreement() {
		return null;
	}

	
	@Override
	public void evictHedgingProfileByOrderType(String orderType) {
		
	}

	@Override
	public void evictReferenceListByType(String orderType) {
	}

    @Override
    public Double getOverriddenPriceVariationThreshold(String buId, String currencyPairId) {
        return null;
    }

    @Override
    public void evictBusinessUnitDistributionConfiguration(String buId, String channel) {
        throw new RuntimeException("Not implemented");
    }

	@Override
	public void evictAuctionMinimumMinutesToSessionTime(String currencyPairId) {
        throw new RuntimeException("Not implemented");
	}

	@Override
	public void evictOverriddenPipConfiguration(String buId, String currencyPairId) {
        throw new RuntimeException("Not implemented");
	}

	@Override
	public void evictOverriddenPriceVariationThreshold(String buId, String currencyPairId) {
        throw new RuntimeException("Not implemented");		
	}

	

	@Override
	public void evictMarketStatusSchedule() {
        throw new RuntimeException("Not implemented");
	}

	@Override
	public void evictApplyMinimumSpread(String categoryId) {
        throw new RuntimeException("Not implemented");

	}

	@Override
	public void evictAuctionCommission(String buId, String currencyPairId, String mksOperation) {
        throw new RuntimeException("Not implemented");		
	}

	@Override
	public void evictUKAuctionHolidays(String currencyPairId, String specificTime) {
        throw new RuntimeException("Not implemented");		
	}

	@Override
	public void evictOrderStaleCheckPeriodInSeconds() {
        throw new RuntimeException("Not implemented");

	}

	@Override
	public void evictMaxStalledTimeInSeconds() {
        throw new RuntimeException("Not implemented");
		
	}

	@Override
	public void evictAllBusinessUnits() {
        throw new RuntimeException("Not implemented");
		
	}

	@Override
	public void evictBusinessUnitsByTradingCategory(String categoryId) {
        throw new RuntimeException("Not implemented");
		
	}

	@Override
	public void evictEODHedgingClosingTime(String currencyPairId) {
        throw new RuntimeException("Not implemented");
		
	}

	@Override
	public void evictAllCurrency() {       
		throw new RuntimeException("Not implemented");
		
	}

	@Override
	public void evictCurrency(String currencyId) {
        throw new RuntimeException("Not implemented");
		
	}

	@Override
	public void evictLPBands(String currencyPairId) {
        throw new RuntimeException("Not implemented");
		
	}

	@Override
	public void evictAllProducts() {
        throw new RuntimeException("Not implemented");
		
	}

	@Override
	public void evictReferenceByTypeAndValue(String type, String value) {
        throw new RuntimeException("Not implemented");
		
	}

	@Override
	public void evictReferenceByTypeAndKey(String type, String key) {
        throw new RuntimeException("Not implemented");
		
	}

	@Override
	public void evictRootBusinessUnit() {
        throw new RuntimeException("Not implemented");
		
	}

	@Override
	public void evictUnitOfMeasureByCode(String code) {
        throw new RuntimeException("Not implemented");
		
	}

	@Override
	public void evictActiveUserAgreement() {
        throw new RuntimeException("Not implemented");
		
	}


	@Override
	public void evictMinimalUsersByBusinessUnit(String buId) {
        throw new RuntimeException("Not implemented");
		
	}

	@Override
	public void evictUserProfileByUserId(int userId) {
        throw new RuntimeException("Not implemented");
		
	}
    @Override
    public int getMaxFailedLoginAttempts() {
        return 3;
    }

    @Override
    public boolean isEODClosingActivated() {
        return isEODActivated;
    }

    @Override
    public List<BusinessUnit> getLPBusinessUnits() {
        throw new RuntimeException("Not implemented");
    }

    @Override
    public String getMDSRepositoryStoreFile() {
        return "var/mds/mds.ser";
    }

	@Override
	public String getDealPendingBookingAlertCronExpression() {
		return "1 0 * * *";
	}

	@Override
	public boolean isUiDisabledForCustomer() {
		return false;
	}
	
	@Override
	public boolean isDisplayHedgeBookingId() {
		return false;
	}

	@Override
	public String getBuIdByPK(Integer buId) {
		return null;
	}

	@Override
	public String getUserIdByPK(Integer userId) {
		return null;
	}

	@Override
	public Set<CacheRefreshInstruction> getAllCacheRefreshInstruction(Date datetime) {
		return null;
	}

    @Override
    public List<User> getMessageRecipientList(String buId, MessageType messageType) {
        throw new RuntimeException("Not implemented");
    }

    @Override
    public void evictMessageRecipientList(String buId, String messageType) {
        throw new RuntimeException("Not implemented");
    }

	@Override
	public Integer getBUPKByUserIdByPK(Integer userId) {
		return null;
	}

	@Override
	public List<User> getMinimalUsersByBusinessUnit(String buId) {
		// TODO Auto-generated method stub
		return null;
	}

    @Override
    public long getZombieOrderCheckPeriodMillis() {
        return 60000l;
    }

    @Override
    public long getZombieOrderThresholdMillis() {
        return 60000;
    }

    @Override
    public void setPriceProvider(IMarketDataSnapshotProvider priceProvider) {
        
    }

	@Override
	public List<String> getSpreadDefinedCurrencyPair() {
		return null;
	}

	@Override
	public boolean isFindurBookingDisabled() {
		return false;
	}
   
    @Override
    public RFQConfiguration getRFQConfiguration(String buId, String currencyPairId) {
        return new RFQConfiguration(RFQMode.AUTO, buId, currencyPairId, 10, 10d, 10d, MarginType.PERCENTAGE);
    }

    @Override
    public OfflinePriceConfiguration getOfflinePriceConfiguration(String currencyPairId) {
        return offlinePriceConfig;
    }

    public void setOfflinePriceConfiguration(OfflinePriceConfiguration offlinePriceConfig) {
        this.offlinePriceConfig = offlinePriceConfig;
    }

	@Override
	public void evictRFQConfiguration(String buId, String currencyPairId) {
	}

	@Override
	public List<FixSessionConfiguration> getActiveFixSessions() {
		return null;
	}

	@Override
	public DefaultFixConfiguration getDefaultFixConfiguration() {
		return null;
	}

    @Override
    public Double getMinimumSpread(String currencyPairId) {
        
        CurrencyPair currencyPair = currencyPairs.stream().filter(cp -> cp.getCurrencyPairId().equals(currencyPairId)).findFirst().orElse(null);
        
        if (currencyPair == null ) {
            return 0d;
        }
        
        if ( currencyPair.isSynthetic() ) {
            return getMinimumSpread(currencyPair.getSyntheticBaseCurrencyPairId());
        } else if( currencyPair.isCross() ){
            CurrencyPair crossLeg1CurrencyPair = getCurrencyPair(currencyPair.getCrossLeg1CurrencyPairId());  
            return crossLeg1CurrencyPair.getMinimumSpread();
            
        } else {
            return currencyPair.getMinimumSpread();
        }  
        
    }

	@Override
	public BusinessUnit getHedgingBusinessUnit() {
		return getRootBusinessUnit();
	}

	@Override
	public void evictHedgingProfileByOrderTypeAndCurrencyPair(String orderType, String currencyPairId) {
	}

	@Override
	public List<Device> getUserDevices(String userId, String appId) {
		return null;
	}

	@Override
	public void evictUserDevice(String userId, String appId) {}

    @Override
    public String getAMQBrokerURL() {
        return ActiveMQLocalBroker.DEFAULT_CONNECTOR_LOCAL_URL;
    }

	@Override
	public List<Product> getAllProducts() {
		return null;
	}
	
	@Override
	public Long getPriceEngineOfflineStreamPeriodMillis() {
	    return 100l;
	}

	@Override
	public double getMaximumDeviationOnOfflineUpdate() {
		return 0;
	}

	@Override
	public List<OfflinePriceConfiguration> getAllOfflinePriceConfiguration() {
		return null;
	}

	@Override
	public String getMarketStatusTransitionNotificationList() {
		return null;
	}

	@Override
	public boolean getIsMarketStatusTransitionEnabled() {
		return false;
	}

    @Override
    public Set<String> getOfflineBaseCurrencyPairs() {
        return new HashSet<String>(Arrays.asList("XAU/USD", "EUR/USD"));
    }

    @Override
    public Set<String> getOfflineDerivedCurrencyPairs() {
        return new HashSet<String>(Arrays.asList("XAU/EUR"));
    }

    @Override
    public Map<String, HedgingConfiguration> getFOKHedgingConfigurationsByCurrencyPair() {
        return new HashMap<>();
    }

	@Override
	public List<HedgeProfile> getAllHedgingProfiles() {
		return new ArrayList<>();
	}
    
    @Override
    public List<Category> getAllTradingCategories() {
        return new ArrayList<>();
    }

    @Override
    public List<AuctionCommissionOverride> getAllAuctionCommissionOverrides() {
        return new ArrayList<>();
    }

    @Override
    public StaticPrice getStaticPrice(String currencyPairId, StaticPriceType type) {
        return null;
    }

    @Override
    public List<StaticPrice> getAllStaticPricesOfType(StaticPriceType type) {
        return new ArrayList<>();
    }

	@Override
	public void evictStaticPrice(String currencyPairId, String type) {
	}

	@Override
	public void evictAllStaticPricesOfType(String type) {
	}

	@Override
	public void evictAllHedgingProfiles() {
	}

	@Override
	public void evictRootBusinessUnitCurrencyPair(CurrencyPair currencyPair) {
	}

    @Override
    public List<String> getOfflineTradingAllowedBUList() {
        return Arrays.asList("PGD");
    }

    @Override
    public void evictAllBookingAggregationInstruction() {
    }

    @Override
    public String getUserPreferences(String appId, String user, Channel channel, UserPreferenceType userPreferenceType) {
        return null;
    }

    @Override
    public boolean shouldWeDropCrossedPrices() {
        return false;
    }

	@Override
	public Double getCrossedPriceThresholdPct() {
		return crossedPriceThresholdPct;
	}

    @Override
    public List<String> getStaleQuoteExcludedBUList() {
        return Arrays.asList("PGD");
    }

    @Override
    public List<String> getPVTExcludedBUList() {
        return Arrays.asList("PGD");
    }

	@Override
	public List<String> getBUListForEODCustomerDealRecap() {
		return Arrays.asList("BU");
	}

    @Override
    public boolean isFindurOZBookingEnabled() {return false;}


    @Override
    public List<PriceVariationThresholdOverride> getAllPriceVariationThresholdOverrides() {
        return null;
    }

	@Override
	public String getDeviceCredentials(String appId, String deviceId) {
		return null;
	}

	@Override
	public CurrencyPair getBusinessUnitCurrencyPair(String buId, String currencyPairId) {
		CurrencyPair cp = new CurrencyPair();
		cp.setCurrencyPairId(currencyPairId);
		cp.setTradingStatus(TradingStatus.TRADABLE);
		cp.setMinimumTradeQuantityInBaseUnits(0.001d);
		cp.setMaximumTradeQuantityInBaseUnits(200000d);
		Currency c = new Currency();
		c.setCurrencyType(CurrencyType.METAL);
		cp.setLeftCurrency(c);
		return cp;
	}

	public List<CurrencyPair> getCurrencyPairs() {
		return currencyPairs;
	}

	public void setCurrencyPairs(List<CurrencyPair> currencyPairs) {
		this.currencyPairs = currencyPairs;
	}

	@Override
	public String getCustomerEODRecapCronExpression() {
		return "1 17 * * *";
	}

	@Override
	public ZoneId getCustomerEODRecapCronTimeZoneId() {
		return ZoneId.of("America/New_York");
	}

	@Override
	public long getPersistenceQueueRedeliveryInitialRedeliveryDelay() {
		return 1000L;
	}

	@Override
	public boolean isPersistenceQueueRedeliveryUseExponentialBackoff() {
		return true;
	}

	@Override
	public double getPersistenceQueueRedeliveryBackOffMultiplier() {
		return 2;
	}

	@Override
	public int getPersistenceQueueRedeliveryMaximumRedeliveries() {
		return 10;
	}

	@Override
	public long getPersistenceQueueRedeliveryRedeliveryDelay() {
		return 1000L;
	}

	@Override
	public String getBuCurrencyPairIdByPK(Integer buCPId) {
		return null;
	}

	@Override
	public void evictBusinessUnitCurrencyPair(String buId, String currencyPairId) {
	}

    @Override
    public PrimaryStatus getPrimaryStatus() {
        return null;
    }

    @Override
    public String getEventBusBookingQueueName() {
        return null;
    }

    @Override
    public String getEventBusAutoHedgerQueueName() {
        return null;
    }

    @Override
    public IHolidayCalendar getHolidayCalendar() {
        return holidayCalendar ;
    }

	@Override
	public List<ForwardCurve> getAllForwardCurves() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ForwardCurve getForwardCurve(String currencyPairId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void evictAllForwardCurves() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void evictForwardCurve(String currencyPairId) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public boolean isBUExposureLimitCheckEnabled() {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public List<User> getRelationshipManagersOfBusinessUnit(String buId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void evictRelationshipManagersOfBusinessUnit(String buId) {
		// TODO Auto-generated method stub
		
	}
	
}
