package ch.mks.wta4.um.limitCheck;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collection;
import java.util.stream.Collectors;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.Limit;
import ch.mks.wta4.ita.model.BusinessUnitExposure;
import ch.mks.wta4.ita.model.Order;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.limitcheck.ILimitCheckService.LimitBreachCode;
import ch.mks.wta4.limitcheck.ILimitCheckService.LimitCheckReport;
import ch.mks.wta4.notification.IEmailNotificationService;
import ch.mks.wta4.um.AbstractMockConfigurationTest;
import ch.mks.wta4.um.exposure.IBusinessUnitExposureEngine;
import ch.mks.wta4.um.priceengine.IPriceProvider;
import ch.mks.wta4.um.session.ISessionTracker;
import ch.mks.wta4.um.session.Session;

@RunWith(Parameterized.class)
public class ExposureLimitCheckTest extends AbstractMockConfigurationTest{
    @Rule public MockitoRule mockitoRule = MockitoJUnit.rule();
    @Mock IPriceProvider priceProvider;
    @Mock IEmailNotificationService emailNotificationService;
    @Mock ISessionTracker sessionTracker;
    @Mock Session session;
    @Mock IBusinessUnitExposureEngine businessUnitExposureEngine;
    private Limit userUSDLimit;
    private Limit buUSDLimit;
    private OrderType orderType;
    private LimitBreachCode[] expectedLimitBreaches;
    private LimitCheckReport report;
    private Order order;
	

    @Parameters
    public static Collection<OrderType> params() {
        return Arrays.asList(OrderType.values()).stream().filter(ot -> ot != OrderType.MARKET).collect(Collectors.toList());
    }
    
    public ExposureLimitCheckTest(OrderType orderType) {
        this.orderType = orderType;
    }
    
    @Before
    public void setup() {
        super.setup();
        userUSDLimit = new Limit();
        buUSDLimit = new Limit();
        user.setLimit(userUSDLimit);
        bu.setLimit(buUSDLimit);
        when(priceProvider.getPrice(any(), any(), any(), eq(true))).thenReturn(null);
        when(sessionTracker.getSession(any())).thenReturn(session);
    }
    
    @Test
    public void testUserTransactionLimit() {
        Double lastPrice = 1000d;
        Double exposure = 1000d;
		when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure));
        ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
        
        order = getDefaultOrder(orderType, user, cp, p);
        
        configPrice(order, lastPrice);
        
        Double qty= 10d;
        order.setBaseQuantity(qty);
        
        double transactionLimit = qty * lastPrice + 1;
        userUSDLimit.setTransactionLimit(transactionLimit);
        buUSDLimit.setDailyNetTransactionLimit(10*transactionLimit);
        buUSDLimit.setTransactionLimit(10*transactionLimit);
        
        report = limitCheckService.checkLimits(order);
        assertTrue(report.isLimitCheckPassed());
        
        configPrice(order, lastPrice + 1);
        report = limitCheckService.checkLimits(order);
        expectedLimitBreaches = new LimitBreachCode[] {LimitBreachCode.USER_TRANSACTION_LIMIT_BREACH};
        assertLimitBreaches(expectedLimitBreaches , report);
 
    }
    
    private void configPrice(Order o, Double p) {
        if ( o.getType() != OrderType.AUCTION ) {
            o.setLimitPrice(p);
        } else {
            when(priceProvider.getPrice(any(), any(), any(), eq(true))).thenReturn(p);
        }
    }

    @Test
    public void testUserTransactionLimitNoLimit() {
        Double lastPrice = 1000d;
        Double exposure = 1000d;
		when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure));
        ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
        order = getDefaultOrder(orderType, user, cp, p);
        order.setBaseQuantity(10d);
        configPrice(order, lastPrice);
        
        
        userUSDLimit.setTransactionLimit(-1d);
        buUSDLimit.setDailyNetTransactionLimit(1000000d);
        buUSDLimit.setTransactionLimit(1000000d);
        report = limitCheckService.checkLimits(order);
        assertTrue(report.getLimitBreachesAsString(), report.isLimitCheckPassed());
    }
    
    @Test
    public void testBUTransactionLimit() {
        Double lastPrice = 1000d;
        Double exposure = 1000d;
		when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure));
        ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
        
        order = getDefaultOrder(orderType, user, cp, p);
        
        configPrice(order, lastPrice);
        
        Double qty= 10d;
        order.setBaseQuantity(qty);
        
        double transactionLimit = qty * lastPrice + 1;
        userUSDLimit.setTransactionLimit(100*transactionLimit);
        buUSDLimit.setDailyNetTransactionLimit(100*transactionLimit);
        buUSDLimit.setTransactionLimit(transactionLimit);
        
        report = limitCheckService.checkLimits(order);
        assertTrue(report.isLimitCheckPassed());
        
        configPrice(order, lastPrice + 1);
        report = limitCheckService.checkLimits(order);
        expectedLimitBreaches = new LimitBreachCode[] {LimitBreachCode.BU_TRANSACTION_LIMIT_BREACH};
        assertLimitBreaches(expectedLimitBreaches , report);
    }
    
    @Test
    public void testBUTransactionLimitsNoLimit() {
        Double lastPrice = 1000d;
        when(priceProvider.getPrice(any(), any(), any(), eq(true))).thenReturn(lastPrice);
        Double exposure = 1000d;
		when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure));
        ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
        order = getDefaultOrder(orderType, user, cp, p);
        configPrice(order, lastPrice);
        order.setBaseQuantity(100000d);
        userUSDLimit.setTransactionLimit(-1d);
        buUSDLimit.setDailyNetTransactionLimit(-1d);
        buUSDLimit.setTransactionLimit(-1d);
        report = limitCheckService.checkLimits(order);
        assertTrue(report.getLimitBreachesAsString(), report.isLimitCheckPassed());    }
    
    @Test
    public void testBUExposureLimit() {
        Double lastPrice = 1000d;
        Double exposure = 1000d;
		when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure));
        ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
        
        order = getDefaultOrder(orderType, user, cp, p);
        configPrice(order, lastPrice);
        
        Double qty= 10d;
        order.setBaseQuantity(qty);
        
        double transactionLimit = qty * lastPrice + 1;
        userUSDLimit.setTransactionLimit(100*transactionLimit);
        buUSDLimit.setDailyNetTransactionLimit(exposure + transactionLimit);
        buUSDLimit.setTransactionLimit(100*transactionLimit);
        
        report = limitCheckService.checkLimits(order);
        assertTrue(report.isLimitCheckPassed());
        
        when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure + transactionLimit));
        report = limitCheckService.checkLimits(order);
        expectedLimitBreaches = new LimitBreachCode[] {LimitBreachCode.BU_EXPOSURE_LIMIT_BREACH};
        assertLimitBreaches(expectedLimitBreaches , report);
        
    }
    
    protected void assertLimitBreaches(LimitBreachCode[] expectedLimitBreaches, LimitCheckReport report) {
        
        assertFalse(report.isLimitCheckPassed());
        
        for ( LimitBreachCode expectedLimitBreachCode:expectedLimitBreaches) {
            assertTrue(report.toString(), report.getLimitBreaches().stream().anyMatch(bc-> bc == expectedLimitBreachCode));
        }
        assertEquals(report.getLimitBreachesAsString(), expectedLimitBreaches.length, report.getLimitBreaches().size());
    }
    
    @Test
    public void testNoLPPriceFound() {
    	ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
        order = getDefaultOrder(orderType, user, cp, p);
        configPrice(order, null);
        report = limitCheckService.checkLimits(order);
        expectedLimitBreaches = new LimitBreachCode[] {LimitBreachCode.TECHNICAL_ERROR_NO_REFERENCE_PRICE};
        assertLimitBreaches(expectedLimitBreaches , report);
        
    }
    
    @Test
    public void testNoExposureFound() {
		when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenReturn(null);
        ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
        
        userUSDLimit.setTransactionLimit(-1d);
        buUSDLimit.setDailyNetTransactionLimit(1d);
        buUSDLimit.setTransactionLimit(-1d);
        
        order = getDefaultOrder(orderType, user, cp, p);
        configPrice(order, 1000d);
        report = limitCheckService.checkLimits(order);
        expectedLimitBreaches = new LimitBreachCode[] {LimitBreachCode.TECHNICAL_ERROR_NO_EXPOSURE_FOUND};
        assertLimitBreaches(expectedLimitBreaches , report);
        
    }
    
    @Test
    public void testEmailSentOn80PCTOfExposureUsed() {
        
        Double threshold = 0.8;
        Double lastPrice = 100d;
        double transactionLimit = 1000;
        Double exposure = transactionLimit * threshold + 1;
		when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure));
        
        ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
        
        order = getDefaultOrder(orderType, user, cp, p);
        configPrice(order, lastPrice);
        
        Double qty= 1d;
        order.setBaseQuantity(qty);
                
        userUSDLimit.setTransactionLimit(100*transactionLimit);
        buUSDLimit.setTransactionLimit(100*transactionLimit);
        
        buUSDLimit.setDailyNetTransactionLimit(transactionLimit);
        
        
        report = limitCheckService.checkLimits(order);
        assertTrue(report.isLimitCheckPassed());
        
        if ( orderType == OrderType.LIMIT || orderType == OrderType.STOP_LOSS || orderType == OrderType.AUCTION) {
            verify(emailNotificationService, times(1)).sendPositionWarningOnNewRestingOrder (any(), eq(transactionLimit), eq(exposure));  
        } else {
            verifyNoMoreInteractions(emailNotificationService);
        }
    }
    
    
    @Test
    public void testBUTransactionLimitCross() {

        Double lastPrice = 1000d;
        Double exposure = 1000d;
		when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure));
        ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
        
        order = getDefaultOrder(orderType, user, xcp, p);
        if ( orderType != OrderType.AUCTION ) {
            order.setLimitPrice(lastPrice);
        }
        
        when(priceProvider.getPrice(eq("USD/CHF"), any(), any(), eq(true))).thenReturn(1d);
        when(priceProvider.getPrice(eq("XAU/CHF"), any(), any(), eq(true))).thenReturn(lastPrice);

        
        Double qty= 10d;
        order.setBaseQuantity(qty);
        
        double transactionLimit = qty * lastPrice + 1;
        userUSDLimit.setTransactionLimit(100*transactionLimit);
        buUSDLimit.setDailyNetTransactionLimit(100*transactionLimit);
        buUSDLimit.setTransactionLimit(transactionLimit);
        
        report = limitCheckService.checkLimits(order);
        assertTrue(report.toString(), report.isLimitCheckPassed());
        
        order.setBaseQuantity(qty + 10);

        report = limitCheckService.checkLimits(order);
        
        expectedLimitBreaches = new LimitBreachCode[] {LimitBreachCode.BU_TRANSACTION_LIMIT_BREACH};
        assertLimitBreaches(expectedLimitBreaches , report);
        
        verify( priceProvider, times(2)).getPrice(eq("USD/CHF"), any(), any(), eq(true));
        
    }
    
    
    @Test
    public void testBusinessUnitPositionLimit() {
        Double threshold = 0.8;
        double transactionLimit = 1000;
        Double exposure = transactionLimit * threshold + 1;
        
        BusinessUnit bu = new BusinessUnit();
        bu.setBusinessUnitId("bu1");
                
		when(businessUnitExposureEngine.getBusinessUnitExposure(bu.getBusinessUnitId())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure));
        
        ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
        
        Double usedExposure = limitCheckService.getBusinessUnitPosition(bu.getBusinessUnitId());
        assertEquals(exposure, usedExposure, 0.001);
    }
    
    
    @Test
    public void testBUTransactionLimitCrossDTMAWithDealerSession() {
        if ( orderType == OrderType.DEAL_TICKET) {
            Double lastPriceUSD = 1000d;
            Double exposure = 1000d;
    		when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure));
            ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
            
            order = getDefaultDealTicket(user, xcp, p);
            order.setLimitPrice(lastPriceUSD);
            
            when(priceProvider.getPrice(eq("USD/CHF"), any(), any(), eq(true))).thenReturn(1d);
            
            Double qty= 300d;
            order.setBaseQuantity(qty);
            
            userUSDLimit.setTransactionLimit(100d);
            buUSDLimit.setDailyNetTransactionLimit(100d);
            buUSDLimit.setTransactionLimit(100d);
            
            when(sessionTracker.getSession(any())).thenReturn(session);
            when(session.isDealerSession()).thenReturn(true);
            
            
            report = limitCheckService.checkLimits(order);
            assertTrue(report.isLimitCheckPassed());
            
        }
        
    }
    
    
    @Test
    public void testBUTransactionLimitCrossAUCTIONWithDealerSession() {
        if ( orderType == OrderType.AUCTION) {
            Double lastPrice = 1000d;
            Double exposure = 1000d;
    		when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure));
            ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
            
            order = getDefaultAuction(user, xcp, p);
            order.setLimitPrice(lastPrice);
            
            when(priceProvider.getPrice(eq("USD/CHF"), any(), any(), eq(true))).thenReturn(1d);
            when(priceProvider.getPrice(eq("XAU/CHF"), any(), any(), eq(true))).thenReturn(lastPrice);
            
            Double qty= 300d;
            order.setBaseQuantity(qty);
            
            userUSDLimit.setTransactionLimit(100d);
            buUSDLimit.setDailyNetTransactionLimit(100d);
            buUSDLimit.setTransactionLimit(100d);
            
            when(sessionTracker.getSession(any())).thenReturn(session);
            when(session.isDealerSession()).thenReturn(true);
            
            
            report = limitCheckService.checkLimits(order);
            assertTrue(report.toString(), report.isLimitCheckPassed());
        }
        
    }

    @Test
    public void testBUTransactionLimitCrossFOKWithDealerSession() {
        if ( orderType == OrderType.FOK) {
            Double lastPriceUSD = 1000d;
            Double exposure = 1000d;
    		when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure));
            ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
            
            order = getDefaultOrder(orderType,user, xcp, p);
            order.setLimitPrice(lastPriceUSD);
            
            when(priceProvider.getPrice(eq("USD/CHF"), any(), any(), eq(true))).thenReturn(1d);
            
            Double qty= 300d;
            order.setBaseQuantity(qty);
            
            userUSDLimit.setTransactionLimit(100d);
            buUSDLimit.setDailyNetTransactionLimit(100d);
            buUSDLimit.setTransactionLimit(100d);
            
            when(sessionTracker.getSession(any())).thenReturn(session);
            when(session.isDealerSession()).thenReturn(true);
            
            
            report = limitCheckService.checkLimits(order);
            expectedLimitBreaches = new LimitBreachCode[] {LimitBreachCode.BU_TRANSACTION_LIMIT_BREACH,LimitBreachCode.USER_TRANSACTION_LIMIT_BREACH,LimitBreachCode.BU_EXPOSURE_LIMIT_BREACH};
            assertLimitBreaches(expectedLimitBreaches , report);
        }
        
    }
    
    @Test
    public void testBUTransactionLimitCrossLimitWithDealerSession() {
        if ( orderType == OrderType.LIMIT) {
            Double lastPriceUSD = 1000d;
            Double exposure = 1000d;
    		when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure));
            ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
            
            order = getDefaultOrder(orderType,user, xcp, p);
            order.setLimitPrice(lastPriceUSD);
            
            when(priceProvider.getPrice(eq("USD/CHF"), any(), any(), eq(true))).thenReturn(1d);
            
            Double qty= 300d;
            order.setBaseQuantity(qty);
            
            userUSDLimit.setTransactionLimit(100d);
            buUSDLimit.setDailyNetTransactionLimit(100d);
            buUSDLimit.setTransactionLimit(100d);
            
            when(sessionTracker.getSession(any())).thenReturn(session);
            when(session.isDealerSession()).thenReturn(true);
            
            
            report = limitCheckService.checkLimits(order);
            expectedLimitBreaches = new LimitBreachCode[] {LimitBreachCode.BU_TRANSACTION_LIMIT_BREACH,LimitBreachCode.USER_TRANSACTION_LIMIT_BREACH,LimitBreachCode.BU_EXPOSURE_LIMIT_BREACH};
            assertLimitBreaches(expectedLimitBreaches , report);
        }
        
    }
    
    @Test
    public void testBUTransactionLimitCrossAUCTIONWithCustomerSession() {
        if ( orderType == OrderType.AUCTION) {
            Double lastPriceCHF = 1000d;
            Double exposure = 1000d;
    		when(businessUnitExposureEngine.getBusinessUnitExposure(any())).thenAnswer( args -> new BusinessUnitExposure(args.getArgument(0), "USD", exposure));
            ExposureLimitCheckService limitCheckService = new ExposureLimitCheckService(businessUnitExposureEngine, configuration, priceProvider, emailNotificationService, sessionTracker);
            
            order = getDefaultAuction(user, xcp, p);
            order.setLimitPrice(lastPriceCHF);
            
            when(priceProvider.getPrice(eq("USD/CHF"), any(), any(), eq(true))).thenReturn(1d);
            when(priceProvider.getPrice(eq("XAU/CHF"), any(), any(), eq(true))).thenReturn(lastPriceCHF);
            
            Double qty= 300d;
            order.setBaseQuantity(qty);
            
            userUSDLimit.setTransactionLimit(100d);
            buUSDLimit.setDailyNetTransactionLimit(100d);
            buUSDLimit.setTransactionLimit(100d);
            
            when(sessionTracker.getSession(any())).thenReturn(session);
            when(session.isDealerSession()).thenReturn(false);
            
            report = limitCheckService.checkLimits(order);
            expectedLimitBreaches = new LimitBreachCode[] {LimitBreachCode.BU_TRANSACTION_LIMIT_BREACH,LimitBreachCode.USER_TRANSACTION_LIMIT_BREACH,LimitBreachCode.BU_EXPOSURE_LIMIT_BREACH};
            assertLimitBreaches(expectedLimitBreaches , report);
        }
    }
    
}
